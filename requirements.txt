# Enhanced Financial Model Application Requirements
# =================================================

# Core framework
flet>=0.21.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0

# Visualization
matplotlib>=3.7.0
plotly>=5.15.0

# Document generation
python-docx>=0.8.11
openpyxl>=3.1.0

# Data validation and utilities
pydantic>=2.0.0
python-dateutil>=2.8.0

# Optional: Enhanced chart capabilities
seaborn>=0.12.0
scipy>=1.10.0

# Optional: PDF generation
reportlab>=4.0.0

# Development and testing (optional)
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
